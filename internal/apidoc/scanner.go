package apidoc

import (
	"encoding/json"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// APIEndpoint 表示一个API端点
type APIEndpoint struct {
	Method      string                 `json:"method"`      // HTTP方法
	Path        string                 `json:"path"`        // 路径
	Handler     string                 `json:"handler"`     // 处理器函数名
	Summary     string                 `json:"summary"`     // 摘要
	Description string                 `json:"description"` // 描述
	Tags        []string               `json:"tags"`        // 标签
	Parameters  []Parameter            `json:"parameters"`  // 参数
	Responses   map[string]Response    `json:"responses"`   // 响应
	Security    []map[string][]string  `json:"security"`    // 安全要求
}

// Parameter 表示API参数 (OpenAPI 3.0兼容)
type Parameter struct {
	Name        string      `json:"name"`        // 参数名
	In          string      `json:"in"`          // 参数位置 (query, path, header, body)
	Required    bool        `json:"required"`    // 是否必需
	Description string      `json:"description"` // 描述
	Example     interface{} `json:"example"`     // 示例值
	Schema      *Schema     `json:"schema"`      // 参数的schema定义 (OpenAPI 3.0标准)

	// 内部使用字段，不序列化到JSON
	Type string `json:"-"` // 内部类型标识，用于向后兼容
}

// Response 表示API响应
type Response struct {
	Description string  `json:"description"` // 描述
	Schema      *Schema `json:"schema"`      // 响应schema
	Examples    map[string]interface{} `json:"examples"` // 示例
}

// Schema 表示数据结构 (OpenAPI 3.0兼容)
type Schema struct {
	Type        string             `json:"type"`        // 类型
	Format      string             `json:"format,omitempty"`      // 格式 (int32, int64, float, double, etc.)
	Title       string             `json:"title,omitempty"`       // 标题
	Properties  map[string]*Schema `json:"properties,omitempty"`  // 属性
	Items       *Schema            `json:"items,omitempty"`       // 数组项类型
	Required    []string           `json:"required,omitempty"`    // 必需字段
	Example     interface{}        `json:"example,omitempty"`     // 示例
	Description string             `json:"description,omitempty"` // 描述
}

// Scanner API扫描器
type Scanner struct {
	fileSet     *token.FileSet
	endpoints   []APIEndpoint
	logger      Logger
	routeGroups map[string]string // 存储路由组前缀映射
	structs     map[string]*StructInfo // 存储解析的结构体信息
}

// StructInfo 结构体信息
type StructInfo struct {
	Name        string                 `json:"name"`        // 结构体名称
	Package     string                 `json:"package"`     // 包名
	Fields      []StructField          `json:"fields"`      // 字段列表
	Comment     string                 `json:"comment"`     // 结构体注释
	Schema      *Schema                `json:"schema"`      // 对应的OpenAPI Schema
}

// StructField 结构体字段信息
type StructField struct {
	Name        string      `json:"name"`        // 字段名称
	Type        string      `json:"type"`        // 字段类型
	JSONTag     string      `json:"json_tag"`    // JSON标签
	Comment     string      `json:"comment"`     // 字段注释
	Required    bool        `json:"required"`    // 是否必填
	Description string      `json:"description"` // 字段描述
}

// Logger 日志接口
type Logger interface {
	Info(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Debug(msg string, args ...interface{})
}

// NewScanner 创建新的API扫描器
func NewScanner(logger Logger) *Scanner {
	return &Scanner{
		fileSet:     token.NewFileSet(),
		endpoints:   make([]APIEndpoint, 0),
		logger:      logger,
		routeGroups: make(map[string]string),
		structs:     make(map[string]*StructInfo),
	}
}

// ScanDirectory 扫描目录中的Go文件
func (s *Scanner) ScanDirectory(dir string) error {
	s.logger.Info("开始扫描目录", "directory", dir)
	
	// 扫描路由文件
	routeFiles := []string{
		"internal/routes/routes.go",
		"cmd/enhanced-server/main.go", 
		"cmd/simple-server/main.go",
	}
	
	for _, file := range routeFiles {
		fullPath := filepath.Join(dir, file)
		if err := s.scanFile(fullPath); err != nil {
			s.logger.Error("扫描文件失败", "file", fullPath, "error", err)
			continue
		}
	}
	
	// 扫描处理器文件
	handlerDir := filepath.Join(dir, "internal/handlers")
	if err := s.scanHandlerDirectory(handlerDir); err != nil {
		s.logger.Error("扫描处理器目录失败", "directory", handlerDir, "error", err)
	}

	// 扫描AI服务文件中的结构体
	aiServiceFile := filepath.Join(dir, "internal/ai/service.go")
	if err := s.scanStructsInFile(aiServiceFile); err != nil {
		s.logger.Error("扫描AI服务结构体失败", "file", aiServiceFile, "error", err)
	}

	s.logger.Info("扫描完成", "endpoints_count", len(s.endpoints), "structs_count", len(s.structs))
	return nil
}

// scanFile 扫描单个文件
func (s *Scanner) scanFile(filename string) error {
	node, err := parser.ParseFile(s.fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析文件失败: %w", err)
	}

	// 遍历AST节点查找路由定义
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.CallExpr:
			s.extractRouteFromCall(x)
		case *ast.FuncDecl:
			// 也扫描函数声明，提取处理器信息
			s.extractHandlerInfo(x)
		}
		return true
	})

	return nil
}

// scanHandlerDirectory 扫描处理器目录
func (s *Scanner) scanHandlerDirectory(dir string) error {
	files, err := filepath.Glob(filepath.Join(dir, "*.go"))
	if err != nil {
		return err
	}
	
	for _, file := range files {
		if err := s.scanHandlerFile(file); err != nil {
			s.logger.Error("扫描处理器文件失败", "file", file, "error", err)
		}
	}
	
	return nil
}

// scanHandlerFile 扫描处理器文件
func (s *Scanner) scanHandlerFile(filename string) error {
	node, err := parser.ParseFile(s.fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析处理器文件失败: %w", err)
	}

	// 遍历声明，查找处理器函数和结构体
	for _, decl := range node.Decls {
		switch d := decl.(type) {
		case *ast.FuncDecl:
			s.extractHandlerInfo(d)
		case *ast.GenDecl:
			s.extractStructInfo(d, node.Name.Name)
		}
	}

	return nil
}

// extractRouteFromCall 从函数调用中提取路由信息
func (s *Scanner) extractRouteFromCall(call *ast.CallExpr) {
	if sel, ok := call.Fun.(*ast.SelectorExpr); ok {
		method := sel.Sel.Name

		// 检查是否是HTTP方法
		if !isHTTPMethod(method) {
			return
		}

		// 提取路径和处理器
		if len(call.Args) >= 2 {
			path := extractStringLiteral(call.Args[0])
			handler := extractHandlerName(call.Args[1])

			if path != "" && handler != "" {
				// 构建完整的API路径
				fullPath := s.buildCompleteAPIPath(path, handler)

				endpoint := APIEndpoint{
					Method:  strings.ToUpper(method),
					Path:    fullPath,
					Handler: handler,
					Tags:    []string{extractTagFromPath(fullPath)},
				}

				s.endpoints = append(s.endpoints, endpoint)
				s.logger.Debug("发现API端点", "method", endpoint.Method, "path", endpoint.Path, "handler", endpoint.Handler)
			}
		}
	}
}

// buildCompleteAPIPath 构建完整的API路径
func (s *Scanner) buildCompleteAPIPath(path, handler string) string {
	// 如果路径已经是完整的，直接返回
	if strings.HasPrefix(path, "/api/") {
		return path
	}

	// 根据处理器名称推断路由组前缀
	basePrefix := "/api/v1"

	// 根据处理器名称或路径特征推断具体的路由组
	switch {
	case strings.Contains(handler, "AI") || strings.Contains(handler, "ai"):
		// AI相关处理器
		return basePrefix + "/ai" + path
	case strings.Contains(handler, "Game") || strings.Contains(handler, "World") ||
		 strings.Contains(handler, "Character") || strings.Contains(handler, "Scene"):
		// 游戏相关处理器
		return basePrefix + "/game" + path
	case strings.Contains(handler, "Auth") || strings.Contains(handler, "Login"):
		// 认证相关处理器
		return basePrefix + "/auth" + path
	case strings.Contains(handler, "User") || strings.Contains(handler, "Profile"):
		// 用户相关处理器
		return basePrefix + "/user" + path
	case strings.Contains(handler, "Debug") || strings.Contains(handler, "APIDoc"):
		// 调试和文档相关处理器
		return basePrefix + path
	default:
		// 默认情况，检查路径特征
		if strings.Contains(path, "/worlds") || strings.Contains(path, "/characters") ||
		   strings.Contains(path, "/scenes") || strings.Contains(path, "/games") {
			return basePrefix + "/game" + path
		}
		if strings.Contains(path, "/generate") || strings.Contains(path, "/history") ||
		   strings.Contains(path, "/stats") {
			return basePrefix + "/ai" + path
		}
		// 其他情况直接添加基础前缀
		return basePrefix + path
	}
}

// extractHandlerInfo 从处理器函数中提取信息
func (s *Scanner) extractHandlerInfo(fn *ast.FuncDecl) {
	if fn.Doc == nil {
		return
	}
	
	handlerName := fn.Name.Name
	
	// 解析注释中的API文档
	for _, comment := range fn.Doc.List {
		text := strings.TrimPrefix(comment.Text, "//")
		text = strings.TrimSpace(text)
		
		// 查找对应的endpoint并更新信息
		for i := range s.endpoints {
			if strings.Contains(s.endpoints[i].Handler, handlerName) {
				s.parseCommentAnnotation(text, &s.endpoints[i])
			}
		}
	}
}

// parseCommentAnnotation 解析注释注解
func (s *Scanner) parseCommentAnnotation(comment string, endpoint *APIEndpoint) {
	if strings.HasPrefix(comment, "@Summary") {
		endpoint.Summary = strings.TrimSpace(strings.TrimPrefix(comment, "@Summary"))
	} else if strings.HasPrefix(comment, "@Description") {
		endpoint.Description = strings.TrimSpace(strings.TrimPrefix(comment, "@Description"))
	} else if strings.HasPrefix(comment, "@Tags") {
		tags := strings.TrimSpace(strings.TrimPrefix(comment, "@Tags"))
		endpoint.Tags = strings.Split(tags, ",")
		for i := range endpoint.Tags {
			endpoint.Tags[i] = strings.TrimSpace(endpoint.Tags[i])
		}
	} else if strings.HasPrefix(comment, "@Param") {
		param := s.parseParameterAnnotation(comment)
		if param != nil {
			// 检查是否是请求体参数
			if param.In == "body" {
				// 对于body参数，我们需要特殊处理
				s.handleBodyParameter(param, endpoint)
			} else {
				endpoint.Parameters = append(endpoint.Parameters, *param)
			}
		}
	} else if strings.HasPrefix(comment, "@Success") || strings.HasPrefix(comment, "@Failure") {
		response := s.parseResponseAnnotation(comment)
		if response != nil && endpoint.Responses == nil {
			endpoint.Responses = make(map[string]Response)
		}
		if response != nil {
			code := extractResponseCode(comment)
			endpoint.Responses[code] = *response
		}
	} else if strings.HasPrefix(comment, "@Security") {
		security := strings.TrimSpace(strings.TrimPrefix(comment, "@Security"))
		endpoint.Security = []map[string][]string{
			{security: []string{}},
		}
	}
}

// parseParameterAnnotation 解析参数注解
func (s *Scanner) parseParameterAnnotation(comment string) *Parameter {
	// 支持多种格式的参数注解
	// @Param name in type required description
	// @Param name in type required description default(value)
	// @Param name in type required description example(value)
	// @Param name in object{field1=type1,field2=type2} required description (复杂JSON对象)
	// @Param name body type required description Enums(...) example(...) format(...) (新格式)

	s.logger.Debug("解析参数注解", "comment", comment)

	// 首先尝试解析新格式的参数注解，支持更多属性
	// @Param name body type required description Enums(...) example(...) format(...) default(...) minimum(...) maximum(...)
	newFormatRe := regexp.MustCompile(`@Param\s+(\w+)\s+(\w+)\s+(\w+)\s+(true|false)\s+"([^"]*)"`)
	newMatches := newFormatRe.FindStringSubmatch(comment)

	if len(newMatches) == 6 {
		paramType := newMatches[3]
		param := &Parameter{
			Name:        newMatches[1],
			In:          newMatches[2],
			Type:        paramType, // 内部使用，不序列化
			Required:    newMatches[4] == "true",
			Description: newMatches[5],
		}

		// 解析额外的属性
		s.parseParameterAttributes(comment, param)

		// 检查是否是已知的结构体类型
		if structSchema := s.GetStructSchema(paramType); structSchema != nil {
			// 使用结构体的schema
			param.Schema = structSchema
			s.logger.Info("使用结构体schema", "param_name", param.Name, "struct_type", paramType)
		} else {
			// 根据类型创建OpenAPI 3.0兼容的schema
			param.Schema = s.createSchemaForType(paramType)
		}

		// 应用解析的属性到schema
		s.applyParameterAttributesToSchema(param)

		s.logger.Debug("参数解析成功", "name", param.Name, "type", paramType, "required", param.Required)
		return param
	}

	// 首先尝试解析复杂对象格式：@Param name in object{field1=type1,field2=type2} required "description"
	// 使用更复杂的正则表达式来匹配嵌套的大括号
	complexRe := regexp.MustCompile(`@Param\s+(\w+)\s+(\w+)\s+(object\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})\s+(true|false)\s+"([^"]*)"`)
	complexMatches := complexRe.FindStringSubmatch(comment)

	if len(complexMatches) == 6 {
		s.logger.Debug("成功匹配复杂参数", "matches", complexMatches)
		return s.parseComplexParameterAnnotation(complexMatches)
	}

	s.logger.Debug("参数注解解析失败", "comment", comment)
	return nil
}

// parseParameterAttributes 解析参数的额外属性
func (s *Scanner) parseParameterAttributes(comment string, param *Parameter) {
	// 解析枚举值 Enums(value1,value2,value3)
	if enumMatch := regexp.MustCompile(`Enums\(([^)]+)\)`).FindStringSubmatch(comment); len(enumMatch) == 2 {
		enumValues := strings.Split(enumMatch[1], ",")
		for i := range enumValues {
			enumValues[i] = strings.TrimSpace(enumValues[i])
		}
		param.Schema = &Schema{Type: param.Type}
		// 注意：OpenAPI 3.0中枚举值存储在schema中，这里先存储到临时字段
		param.Type = param.Type + "|enum:" + enumMatch[1]
	}

	// 解析示例值 example(value)
	if exampleMatch := regexp.MustCompile(`example\(([^)]+)\)`).FindStringSubmatch(comment); len(exampleMatch) == 2 {
		param.Example = s.parseExampleValue(exampleMatch[1])
	}

	// 解析格式 format(uuid)
	if formatMatch := regexp.MustCompile(`format\(([^)]+)\)`).FindStringSubmatch(comment); len(formatMatch) == 2 {
		if param.Schema == nil {
			param.Schema = &Schema{Type: param.Type}
		}
		param.Schema.Format = formatMatch[1]
	}

	// 解析默认值 default(value)
	if defaultMatch := regexp.MustCompile(`default\(([^)]+)\)`).FindStringSubmatch(comment); len(defaultMatch) == 2 {
		if param.Example == nil {
			param.Example = s.parseExampleValue(defaultMatch[1])
		}
	}

	// 解析最小值 minimum(value)
	if minMatch := regexp.MustCompile(`minimum\(([^)]+)\)`).FindStringSubmatch(comment); len(minMatch) == 2 {
		if param.Schema == nil {
			param.Schema = &Schema{Type: param.Type}
		}
		// 存储到临时字段，稍后处理
		param.Type = param.Type + "|min:" + minMatch[1]
	}

	// 解析最大值 maximum(value)
	if maxMatch := regexp.MustCompile(`maximum\(([^)]+)\)`).FindStringSubmatch(comment); len(maxMatch) == 2 {
		if param.Schema == nil {
			param.Schema = &Schema{Type: param.Type}
		}
		// 存储到临时字段，稍后处理
		param.Type = param.Type + "|max:" + maxMatch[1]
	}
}

// parseExampleValue 解析示例值，尝试转换为正确的类型
func (s *Scanner) parseExampleValue(value string) interface{} {
	// 去掉引号
	value = strings.Trim(value, `"'`)

	// 尝试解析为JSON
	if strings.HasPrefix(value, "{") || strings.HasPrefix(value, "[") {
		var jsonValue interface{}
		if err := json.Unmarshal([]byte(value), &jsonValue); err == nil {
			return jsonValue
		}
	}

	// 尝试解析为数字
	if intValue, err := strconv.Atoi(value); err == nil {
		return intValue
	}

	if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
		return floatValue
	}

	// 尝试解析为布尔值
	if boolValue, err := strconv.ParseBool(value); err == nil {
		return boolValue
	}

	// 默认返回字符串
	return value
}

// applyParameterAttributesToSchema 将解析的属性应用到schema
func (s *Scanner) applyParameterAttributesToSchema(param *Parameter) {
	if param.Schema == nil {
		return
	}

	// 处理存储在Type字段中的额外信息
	typeParts := strings.Split(param.Type, "|")
	baseType := typeParts[0]
	param.Schema.Type = baseType

	for i := 1; i < len(typeParts); i++ {
		part := typeParts[i]
		if strings.HasPrefix(part, "enum:") {
			enumStr := strings.TrimPrefix(part, "enum:")
			enumValues := strings.Split(enumStr, ",")
			var enums []interface{}
			for _, enumValue := range enumValues {
				enums = append(enums, strings.TrimSpace(enumValue))
			}
			// 注意：这里应该设置enum字段，但Schema结构体中没有定义，暂时跳过
		} else if strings.HasPrefix(part, "min:") {
			// 处理最小值，暂时跳过，因为Schema结构体中没有定义相关字段
		} else if strings.HasPrefix(part, "max:") {
			// 处理最大值，暂时跳过，因为Schema结构体中没有定义相关字段
		}
	}

	// 设置示例值
	if param.Example != nil {
		param.Schema.Example = param.Example
	}
}

// handleBodyParameter 处理请求体参数
func (s *Scanner) handleBodyParameter(param *Parameter, endpoint *APIEndpoint) {
	// 查找是否已经有body参数
	var bodyParam *Parameter
	for i := range endpoint.Parameters {
		if endpoint.Parameters[i].In == "body" {
			bodyParam = &endpoint.Parameters[i]
			break
		}
	}

	// 如果没有body参数，创建一个
	if bodyParam == nil {
		newBodyParam := Parameter{
			Name:        "request",
			In:          "body",
			Required:    true,
			Description: "请求体参数",
			Schema: &Schema{
				Type:       "object",
				Properties: make(map[string]*Schema),
				Required:   make([]string, 0),
			},
		}
		endpoint.Parameters = append(endpoint.Parameters, newBodyParam)
		bodyParam = &endpoint.Parameters[len(endpoint.Parameters)-1]
	}

	// 如果body参数没有schema，创建一个
	if bodyParam.Schema == nil {
		bodyParam.Schema = &Schema{
			Type:       "object",
			Properties: make(map[string]*Schema),
			Required:   make([]string, 0),
		}
	}

	// 将当前参数作为body schema的属性添加
	if bodyParam.Schema.Properties == nil {
		bodyParam.Schema.Properties = make(map[string]*Schema)
	}

	// 添加字段到body schema
	bodyParam.Schema.Properties[param.Name] = param.Schema

	// 如果是必填字段，添加到required列表
	if param.Required {
		bodyParam.Schema.Required = append(bodyParam.Schema.Required, param.Name)
	}

	// 更新body参数的描述
	if param.Description != "" {
		if bodyParam.Schema.Properties[param.Name] != nil {
			bodyParam.Schema.Properties[param.Name].Description = param.Description
		}
	}
}

// parseComplexParameterAnnotation 解析复杂参数注解（JSON对象）
func (s *Scanner) parseComplexParameterAnnotation(matches []string) *Parameter {
	param := &Parameter{
		Name:        matches[1],
		In:          matches[2],
		Type:        "object", // 内部使用，不序列化
		Required:    matches[4] == "true",
		Description: matches[5],
	}

	// 解析复杂对象定义，如 object{field1=type1,field2=type2}
	objectDef := matches[3]
	param.Schema = s.parseComplexObjectSchema(objectDef)

	return param
}

// parseComplexObjectSchema 解析复杂对象Schema
func (s *Scanner) parseComplexObjectSchema(objectDef string) *Schema {
	schema := &Schema{
		Type:       "object",
		Properties: make(map[string]*Schema),
	}

	// 提取大括号内的内容
	start := strings.Index(objectDef, "{")
	end := strings.LastIndex(objectDef, "}")
	if start == -1 || end == -1 || start >= end {
		return schema
	}

	content := objectDef[start+1 : end]

	// 智能分割字段定义，处理嵌套的大括号
	fields := s.splitFieldsWithBraces(content)
	var required []string

	for _, field := range fields {
		field = strings.TrimSpace(field)
		if field == "" {
			continue
		}

		// 解析字段定义：field=type 或 field=type(required)
		parts := strings.SplitN(field, "=", 2)
		if len(parts) != 2 {
			continue
		}

		fieldName := strings.TrimSpace(parts[0])
		fieldType := strings.TrimSpace(parts[1])

		// 检查是否是必需字段
		isRequired := false
		if strings.Contains(fieldType, "(required)") {
			isRequired = true
			fieldType = strings.Replace(fieldType, "(required)", "", -1)
			fieldType = strings.TrimSpace(fieldType)
		}

		// 创建字段Schema
		var fieldSchema *Schema
		if strings.HasPrefix(fieldType, "object{") {
			// 递归解析嵌套对象
			fieldSchema = s.parseComplexObjectSchema(fieldType)
		} else {
			fieldSchema = s.createSchemaForType(fieldType)
		}
		fieldSchema.Description = fmt.Sprintf("%s类型的字段", fieldType)

		schema.Properties[fieldName] = fieldSchema

		if isRequired {
			required = append(required, fieldName)
		}
	}

	if len(required) > 0 {
		schema.Required = required
	}

	return schema
}

// splitFieldsWithBraces 智能分割字段定义，处理嵌套的大括号
func (s *Scanner) splitFieldsWithBraces(content string) []string {
	var fields []string
	var current strings.Builder
	braceCount := 0

	for _, char := range content {
		switch char {
		case '{':
			braceCount++
			current.WriteRune(char)
		case '}':
			braceCount--
			current.WriteRune(char)
		case ',':
			if braceCount == 0 {
				// 只有在大括号平衡时才分割
				fields = append(fields, current.String())
				current.Reset()
			} else {
				current.WriteRune(char)
			}
		default:
			current.WriteRune(char)
		}
	}

	// 添加最后一个字段
	if current.Len() > 0 {
		fields = append(fields, current.String())
	}

	return fields
}

// createSchemaForType 根据类型创建OpenAPI 3.0兼容的schema
func (s *Scanner) createSchemaForType(paramType string) *Schema {
	schema := &Schema{}

	// 根据类型设置正确的OpenAPI类型和示例值
	switch paramType {
	case "int", "integer":
		schema.Type = "integer"
		schema.Format = "int32"
		schema.Example = 1
	case "int64":
		schema.Type = "integer"
		schema.Format = "int64"
		schema.Example = 1
	case "string":
		schema.Type = "string"
		schema.Example = "example"
	case "bool", "boolean":
		schema.Type = "boolean"
		schema.Example = true
	case "float", "float32":
		schema.Type = "number"
		schema.Format = "float"
		schema.Example = 1.0
	case "float64", "number":
		schema.Type = "number"
		schema.Format = "double"
		schema.Example = 1.0
	case "object":
		schema.Type = "object"
		schema.Example = map[string]interface{}{}
		schema.Description = "JSON对象"
	case "array":
		schema.Type = "array"
		schema.Items = &Schema{
			Type: "object",
		}
		schema.Example = []interface{}{}
		schema.Description = "JSON数组"
	default:
		// 对于复杂类型，设置为object类型并添加描述
		schema.Type = "object"
		schema.Description = fmt.Sprintf("复杂对象类型: %s", paramType)
		// 可以在这里添加对自定义类型的引用
		if paramType != "object" {
			schema.Title = paramType
		}
	}

	return schema
}

// parseResponseAnnotation 解析响应注解
// 支持格式: @Success 200 {object} Response{data=User} "成功响应"
// 支持格式: @Success 200 {object} Response{data=object{user=models.User,token=string}} "成功响应"
// 支持格式: @Failure 400 {object} Response "请求错误"
func (s *Scanner) parseResponseAnnotation(comment string) *Response {
	// 使用正则表达式解析响应注解
	// 格式: @(Success|Failure) code {type} schema "description"
	// 注意：schema部分可能包含嵌套的大括号，如 Response{data=object{user=models.User,token=string}}
	re := regexp.MustCompile(`@(Success|Failure)\s+\d+\s+\{([^}]+)\}\s+([^\s"]+(?:\{[^}]*(?:\{[^}]*\}[^}]*)*\})?)(?:\s+"([^"]*)")?`)
	matches := re.FindStringSubmatch(comment)

	if len(matches) >= 4 {
		responseType := matches[2]    // object, array, string, etc.
		schemaRef := matches[3]      // Response{data=User}, Response, etc.
		description := ""
		if len(matches) > 4 && matches[4] != "" {
			description = matches[4]
		}

		// 创建响应Schema
		schema := s.createResponseSchema(responseType, schemaRef)

		// 如果没有描述，生成默认描述
		if description == "" {
			if strings.Contains(comment, "@Success") {
				description = "成功响应"
			} else {
				description = "错误响应"
			}
		}

		return &Response{
			Description: description,
			Schema:      schema,
		}
	}

	// 如果正则匹配失败，尝试简单解析
	parts := strings.Fields(comment)
	if len(parts) >= 3 {
		description := strings.Join(parts[2:], " ")
		return &Response{
			Description: description,
		}
	}

	return nil
}

// createResponseSchema 创建响应Schema
func (s *Scanner) createResponseSchema(responseType, schemaRef string) *Schema {
	schema := &Schema{}

	switch responseType {
	case "object":
		schema.Type = "object"

		// 解析复杂的schema引用，如 Response{data=User}
		if strings.Contains(schemaRef, "{") {
			schema = s.parseComplexResponseSchema(schemaRef)
		} else {
			// 检查是否是标准的响应类型，如果是则生成统一的响应格式
			if s.isStandardResponseType(schemaRef) {
				schema = s.generateStandardResponseSchema(schemaRef)
			} else {
				// 简单的对象引用
				schema.Title = schemaRef
				schema.Description = fmt.Sprintf("响应对象: %s", schemaRef)
			}
		}

	case "array":
		schema.Type = "array"
		schema.Items = &Schema{
			Type:        "object",
			Title:       schemaRef,
			Description: fmt.Sprintf("数组项类型: %s", schemaRef),
		}

	case "string":
		schema.Type = "string"
		schema.Example = "响应字符串"

	case "integer":
		schema.Type = "integer"
		schema.Example = 1

	case "boolean":
		schema.Type = "boolean"
		schema.Example = true

	default:
		schema.Type = "object"
		schema.Title = schemaRef
		schema.Description = fmt.Sprintf("响应类型: %s", responseType)
	}

	return schema
}

// parseComplexResponseSchema 解析复杂的响应Schema
// 例如: Response{data=User}, Response{data=object{user=models.User,token=string}}
func (s *Scanner) parseComplexResponseSchema(schemaRef string) *Schema {
	schema := &Schema{
		Type:       "object",
		Properties: make(map[string]*Schema),
	}

	// 提取主要类型名称 (Response)
	if idx := strings.Index(schemaRef, "{"); idx > 0 {
		schema.Title = schemaRef[:idx]
		schema.Description = fmt.Sprintf("API响应对象: %s", schema.Title)
	}

	// 解析内部结构 {data=User} 或 {data=object{user=models.User,token=string}}
	if strings.Contains(schemaRef, "data=") {
		// 标准API响应格式
		schema.Properties["success"] = &Schema{
			Type:        "boolean",
			Description: "操作是否成功",
			Example:     true,
		}
		schema.Properties["message"] = &Schema{
			Type:        "string",
			Description: "响应消息",
			Example:     "操作成功",
		}
		schema.Properties["timestamp"] = &Schema{
			Type:        "integer",
			Format:      "int64",
			Description: "时间戳",
			Example:     1609459200,
		}

		// 解析data字段的类型
		dataSchema := s.parseDataFieldSchema(schemaRef)
		if dataSchema != nil {
			schema.Properties["data"] = dataSchema
		}

		schema.Required = []string{"success", "message", "timestamp"}
	}

	return schema
}

// parseDataFieldSchema 解析data字段的Schema
func (s *Scanner) parseDataFieldSchema(schemaRef string) *Schema {
	// 提取data=后面的内容，需要正确处理嵌套的大括号
	// 例如：data=object{user=models.User,token=string,expires_at=string}

	// 找到data=的位置
	dataStart := strings.Index(schemaRef, "data=")
	if dataStart == -1 {
		return nil
	}

	// 从data=后面开始解析
	content := schemaRef[dataStart+5:] // 跳过"data="

	// 如果以object{开头，需要找到匹配的}
	if strings.HasPrefix(content, "object{") {
		// 找到匹配的右大括号
		braceCount := 0
		endPos := -1
		for i, char := range content {
			if char == '{' {
				braceCount++
			} else if char == '}' {
				braceCount--
				if braceCount == 0 {
					endPos = i
					break
				}
			}
		}

		if endPos != -1 {
			content = content[:endPos+1] // 包含最后的}
		}
	} else {
		// 简单类型，找到第一个}或者字符串结尾
		if endPos := strings.Index(content, "}"); endPos != -1 {
			content = content[:endPos]
		}
	}

	dataType := content

	// 处理简单类型 data=User
	if !strings.Contains(dataType, "object{") {
		return &Schema{
			Type:        "object",
			Title:       dataType,
			Description: fmt.Sprintf("数据对象: %s", dataType),
		}
	}

	// 处理复杂对象 data=object{user=models.User,token=string}
	if strings.HasPrefix(dataType, "object{") {
		return s.parseObjectSchema(dataType)
	}

	return &Schema{
		Type:        "object",
		Description: fmt.Sprintf("数据类型: %s", dataType),
	}
}

// parseObjectSchema 解析对象Schema
// 例如: object{user=models.User,token=string,expires_at=string}
func (s *Scanner) parseObjectSchema(objectDef string) *Schema {
	schema := &Schema{
		Type:       "object",
		Properties: make(map[string]*Schema),
	}

	// 提取大括号内的内容
	start := strings.Index(objectDef, "{")
	end := strings.LastIndex(objectDef, "}")
	if start == -1 || end == -1 || start >= end {
		return schema
	}

	content := objectDef[start+1 : end]

	// 分割字段定义
	fields := strings.Split(content, ",")
	for _, field := range fields {
		field = strings.TrimSpace(field)
		if field == "" {
			continue
		}

		// 解析字段定义 user=models.User
		parts := strings.SplitN(field, "=", 2)
		if len(parts) != 2 {
			continue
		}

		fieldName := strings.TrimSpace(parts[0])
		fieldType := strings.TrimSpace(parts[1])

		// 创建字段Schema
		fieldSchema := s.createFieldSchema(fieldType)
		if fieldSchema != nil {
			schema.Properties[fieldName] = fieldSchema
		}
	}

	return schema
}

// createFieldSchema 根据字段类型创建Schema
func (s *Scanner) createFieldSchema(fieldType string) *Schema {
	switch fieldType {
	case "string":
		return &Schema{
			Type:        "string",
			Description: "字符串类型",
			Example:     "示例文本",
		}
	case "int", "integer":
		return &Schema{
			Type:        "integer",
			Description: "整数类型",
			Example:     1,
		}
	case "bool", "boolean":
		return &Schema{
			Type:        "boolean",
			Description: "布尔类型",
			Example:     true,
		}
	case "float", "number":
		return &Schema{
			Type:        "number",
			Description: "数字类型",
			Example:     1.0,
		}
	default:
		// 复杂类型或模型引用
		return &Schema{
			Type:        "object",
			Title:       fieldType,
			Description: fmt.Sprintf("对象类型: %s", fieldType),
		}
	}
}

// GetEndpoints 获取所有扫描到的端点
func (s *Scanner) GetEndpoints() []APIEndpoint {
	return s.endpoints
}

// 辅助函数

// isHTTPMethod 检查是否是HTTP方法
func isHTTPMethod(method string) bool {
	httpMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
	method = strings.ToUpper(method)
	for _, m := range httpMethods {
		if m == method {
			return true
		}
	}
	return false
}

// extractStringLiteral 提取字符串字面量
func extractStringLiteral(expr ast.Expr) string {
	if lit, ok := expr.(*ast.BasicLit); ok && lit.Kind == token.STRING {
		// 去掉引号
		return strings.Trim(lit.Value, `"`)
	}
	return ""
}

// extractHandlerName 提取处理器名称
func extractHandlerName(expr ast.Expr) string {
	switch x := expr.(type) {
	case *ast.Ident:
		return x.Name
	case *ast.SelectorExpr:
		if ident, ok := x.X.(*ast.Ident); ok {
			return ident.Name + "." + x.Sel.Name
		}
	}
	return ""
}

// extractTagFromPath 从路径提取标签
func extractTagFromPath(path string) string {
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) > 0 && parts[0] != "" {
		return strings.Title(parts[0])
	}
	return "API"
}

// extractResponseCode 提取响应代码
func extractResponseCode(comment string) string {
	re := regexp.MustCompile(`@(?:Success|Failure)\s+(\d+)`)
	matches := re.FindStringSubmatch(comment)
	if len(matches) == 2 {
		return matches[1]
	}
	return "200"
}

// isStandardResponseType 检查是否是标准的响应类型
func (s *Scanner) isStandardResponseType(schemaRef string) bool {
	standardTypes := []string{
		"Response",
		"APIResponse",
		"UnifiedAPIResponse",
		"ApiResponse",
	}

	for _, standardType := range standardTypes {
		if schemaRef == standardType {
			return true
		}
	}
	return false
}

// generateStandardResponseSchema 生成标准的统一响应格式Schema
func (s *Scanner) generateStandardResponseSchema(schemaRef string) *Schema {
	return &Schema{
		Type:  "object",
		Title: schemaRef,
		Properties: map[string]*Schema{
			"success": {
				Type:        "boolean",
				Example:     true,
				Description: "操作是否成功",
			},
			"message": {
				Type:        "string",
				Example:     "操作成功",
				Description: "响应消息",
			},
			"data": {
				Type:        "object",
				Description: "响应数据，具体结构根据接口而定",
				Example:     map[string]interface{}{},
			},
			"timestamp": {
				Type:        "integer",
				Format:      "int64",
				Example:     1609459200,
				Description: "时间戳",
			},
			"code": {
				Type:        "integer",
				Example:     200,
				Description: "响应状态码",
			},
		},
		Required: []string{"success", "message", "timestamp"},
		Description: fmt.Sprintf("统一API响应格式: %s", schemaRef),
	}
}

// scanStructsInFile 扫描文件中的结构体定义
func (s *Scanner) scanStructsInFile(filename string) error {
	node, err := parser.ParseFile(s.fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析文件失败: %w", err)
	}

	// 遍历声明，查找结构体
	for _, decl := range node.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok {
			s.extractStructInfo(genDecl, node.Name.Name)
		}
	}

	return nil
}

// extractStructInfo 从通用声明中提取结构体信息
func (s *Scanner) extractStructInfo(genDecl *ast.GenDecl, packageName string) {
	for _, spec := range genDecl.Specs {
		if typeSpec, ok := spec.(*ast.TypeSpec); ok {
			if structType, ok := typeSpec.Type.(*ast.StructType); ok {
				s.parseStruct(typeSpec, structType, genDecl.Doc, packageName)
			}
		}
	}
}

// parseStruct 解析结构体定义
func (s *Scanner) parseStruct(typeSpec *ast.TypeSpec, structType *ast.StructType, doc *ast.CommentGroup, packageName string) {
	structName := typeSpec.Name.Name

	// 只解析请求和响应相关的结构体
	if !s.isAPIStruct(structName) {
		return
	}

	s.logger.Info("解析结构体", "name", structName, "package", packageName)

	structInfo := &StructInfo{
		Name:    structName,
		Package: packageName,
		Fields:  make([]StructField, 0),
		Comment: s.extractComment(doc),
	}

	// 解析字段
	for _, field := range structType.Fields.List {
		s.parseStructField(field, structInfo)
	}

	// 生成对应的OpenAPI Schema
	structInfo.Schema = s.generateSchemaFromStruct(structInfo)

	// 存储结构体信息
	key := fmt.Sprintf("%s.%s", packageName, structName)
	s.structs[key] = structInfo

	s.logger.Info("结构体解析完成", "name", structName, "fields_count", len(structInfo.Fields))
}

// isAPIStruct 判断是否是API相关的结构体
func (s *Scanner) isAPIStruct(structName string) bool {
	apiStructs := []string{
		"GenerateRequest", "GenerateResponse",
		"GenerateSceneRequest", "GenerateCharacterRequest", "GenerateEventRequest",
		"Response", "UnifiedAPIResponse", "APIResponse",
	}

	for _, apiStruct := range apiStructs {
		if strings.Contains(structName, apiStruct) {
			return true
		}
	}

	return false
}

// extractComment 提取注释文本
func (s *Scanner) extractComment(commentGroup *ast.CommentGroup) string {
	if commentGroup == nil {
		return ""
	}

	var comments []string
	for _, comment := range commentGroup.List {
		text := strings.TrimPrefix(comment.Text, "//")
		text = strings.TrimPrefix(text, "/*")
		text = strings.TrimSuffix(text, "*/")
		text = strings.TrimSpace(text)
		if text != "" {
			comments = append(comments, text)
		}
	}

	return strings.Join(comments, " ")
}

// parseStructField 解析结构体字段
func (s *Scanner) parseStructField(field *ast.Field, structInfo *StructInfo) {
	if len(field.Names) == 0 {
		return // 匿名字段，暂时跳过
	}

	fieldName := field.Names[0].Name
	fieldType := s.getTypeString(field.Type)

	// 解析标签
	var jsonTag string
	var required bool
	if field.Tag != nil {
		tagValue := strings.Trim(field.Tag.Value, "`")
		jsonTag = s.extractJSONTag(tagValue)
		required = s.isRequiredField(tagValue)
	}

	// 提取字段注释
	comment := s.extractComment(field.Doc)
	if comment == "" && field.Comment != nil {
		comment = s.extractComment(field.Comment)
	}

	structField := StructField{
		Name:        fieldName,
		Type:        fieldType,
		JSONTag:     jsonTag,
		Comment:     comment,
		Required:    required,
		Description: comment, // 使用注释作为描述
	}

	structInfo.Fields = append(structInfo.Fields, structField)
}

// getTypeString 获取类型字符串
func (s *Scanner) getTypeString(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.StarExpr:
		return "*" + s.getTypeString(t.X)
	case *ast.ArrayType:
		return "[]" + s.getTypeString(t.Elt)
	case *ast.MapType:
		return fmt.Sprintf("map[%s]%s", s.getTypeString(t.Key), s.getTypeString(t.Value))
	case *ast.SelectorExpr:
		return fmt.Sprintf("%s.%s", s.getTypeString(t.X), t.Sel.Name)
	case *ast.InterfaceType:
		return "interface{}"
	default:
		return "unknown"
	}
}

// extractJSONTag 提取JSON标签
func (s *Scanner) extractJSONTag(tagValue string) string {
	re := regexp.MustCompile(`json:"([^"]*)"`)
	matches := re.FindStringSubmatch(tagValue)
	if len(matches) > 1 {
		return strings.Split(matches[1], ",")[0] // 只取字段名，忽略omitempty等选项
	}
	return ""
}

// isRequiredField 判断字段是否必填
func (s *Scanner) isRequiredField(tagValue string) bool {
	return strings.Contains(tagValue, `binding:"required"`)
}

// generateSchemaFromStruct 从结构体信息生成OpenAPI Schema
func (s *Scanner) generateSchemaFromStruct(structInfo *StructInfo) *Schema {
	schema := &Schema{
		Type:        "object",
		Properties:  make(map[string]*Schema),
		Required:    make([]string, 0),
		Description: structInfo.Comment,
	}

	for _, field := range structInfo.Fields {
		fieldName := field.JSONTag
		if fieldName == "" {
			fieldName = strings.ToLower(field.Name) // 默认使用小写字段名
		}

		// 跳过忽略的字段
		if fieldName == "-" {
			continue
		}

		fieldSchema := s.createSchemaForGoType(field.Type)
		fieldSchema.Description = field.Description

		schema.Properties[fieldName] = fieldSchema

		if field.Required {
			schema.Required = append(schema.Required, fieldName)
		}
	}

	return schema
}

// createSchemaForGoType 根据Go类型创建Schema
func (s *Scanner) createSchemaForGoType(goType string) *Schema {
	schema := &Schema{}

	switch {
	case goType == "string":
		schema.Type = "string"
		schema.Example = "示例文本"
	case goType == "int" || goType == "int32":
		schema.Type = "integer"
		schema.Format = "int32"
		schema.Example = 1
	case goType == "int64":
		schema.Type = "integer"
		schema.Format = "int64"
		schema.Example = 1
	case goType == "float32":
		schema.Type = "number"
		schema.Format = "float"
		schema.Example = 1.0
	case goType == "float64":
		schema.Type = "number"
		schema.Format = "double"
		schema.Example = 1.0
	case goType == "bool":
		schema.Type = "boolean"
		schema.Example = true
	case strings.HasPrefix(goType, "[]"):
		schema.Type = "array"
		elementType := strings.TrimPrefix(goType, "[]")
		schema.Items = s.createSchemaForGoType(elementType)
	case strings.HasPrefix(goType, "map["):
		schema.Type = "object"
		schema.Example = map[string]interface{}{}
	case strings.HasPrefix(goType, "*"):
		// 指针类型，递归处理
		baseType := strings.TrimPrefix(goType, "*")
		return s.createSchemaForGoType(baseType)
	case goType == "interface{}" || goType == "map[string]interface{}":
		schema.Type = "object"
		schema.Example = map[string]interface{}{}
	case goType == "uuid.UUID" || goType == "*uuid.UUID":
		schema.Type = "string"
		schema.Format = "uuid"
		schema.Example = "123e4567-e89b-12d3-a456-************"
	default:
		// 未知类型，默认为object
		schema.Type = "object"
		schema.Example = map[string]interface{}{}
	}

	return schema
}

// GetStructs 获取解析的结构体信息
func (s *Scanner) GetStructs() map[string]*StructInfo {
	return s.structs
}

// GetStructSchema 根据结构体名称获取Schema
func (s *Scanner) GetStructSchema(structName string) *Schema {
	for key, structInfo := range s.structs {
		if strings.HasSuffix(key, "."+structName) {
			return structInfo.Schema
		}
	}
	return nil
}
